"""
Contrôleur de détection de phishing pour PICA - Plateforme de cybersécurité automatisée

Ce module fournit les endpoints API pour l'analyse d'URLs suspectes
et la détection de tentatives de phishing en temps réel.
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime
import uuid
import json
import time
import threading
from pymongo import MongoClient
from bson import ObjectId
from app.extensions import mongo
from app.utils.decorators import handle_options, admin_required
from app.services.phishing_service import analyze_url


def convert_objectid_to_string(obj):
    """Convertir récursivement tous les ObjectId en strings dans un objet"""
    if isinstance(obj, ObjectId):
        return str(obj)
    elif isinstance(obj, dict):
        return {key: convert_objectid_to_string(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_objectid_to_string(item) for item in obj]
    else:
        return obj

# ============================================================================
# CONFIGURATION DU BLUEPRINT
# ============================================================================

phishing_bp = Blueprint('phishing', __name__)

# ============================================================================
# STOCKAGE DES ANALYSES EN COURS
# ============================================================================

# Dictionnaire pour stocker les analyses en cours
active_analyses = {}

# ============================================================================
# FONCTIONS UTILITAIRES
# ============================================================================

def save_analysis_to_db(analysis_data):
    """
    Sauvegarde une analyse de phishing dans la base de données.

    Args:
        analysis_data (dict): Données de l'analyse à sauvegarder

    Returns:
        str: ID de l'analyse sauvegardée
    """
    try:
        # Ajouter les métadonnées
        analysis_data['created_at'] = datetime.utcnow()
        analysis_data['analysis_type'] = 'phishing_detection'

        # Insérer dans la collection phishing_analyses
        result = mongo.db.phishing_analyses.insert_one(analysis_data)

        # Vérifier si le lien est suspect (score > 30%) et l'ajouter à la collection des liens suspects
        if 'result' in analysis_data and analysis_data['result']:
            risk_score = analysis_data['result'].get('risk_score', 0)
            if risk_score > 30:
                save_suspicious_link(analysis_data)

        return str(result.inserted_id)
    except Exception as e:
        print(f"Error saving analysis: {str(e)}")
        return None

def save_suspicious_link(analysis_data):
    """
    Sauvegarde un lien suspect dans la collection dédiée aux liens de phishing.

    Args:
        analysis_data (dict): Données de l'analyse contenant le lien suspect
    """
    try:
        url = analysis_data.get('url')
        result = analysis_data.get('result', {})

        # Vérifier si le lien existe déjà dans la collection
        existing_link = mongo.db.suspicious_links.find_one({'url': url})

        if existing_link:
            # Mettre à jour le lien existant avec les nouvelles données
            update_data = {
                'last_detected': datetime.utcnow(),
                'detection_count': existing_link.get('detection_count', 0) + 1,
                'latest_risk_score': result.get('risk_score', 0),
                'latest_likelihood': result.get('likelihood', 'Unknown'),
                'latest_analysis_id': analysis_data.get('analysis_id'),
                'latest_user_id': analysis_data.get('user_id')
            }

            # Mettre à jour le score maximum si nécessaire
            if result.get('risk_score', 0) > existing_link.get('max_risk_score', 0):
                update_data['max_risk_score'] = result.get('risk_score', 0)
                update_data['max_likelihood'] = result.get('likelihood', 'Unknown')

            mongo.db.suspicious_links.update_one(
                {'url': url},
                {'$set': update_data}
            )
            print(f"Lien suspect mis à jour: {url} (Score: {result.get('risk_score')}%)")
        else:
            # Créer un nouveau lien suspect
            suspicious_link_data = {
                'url': url,
                'domain': result.get('domain', ''),
                'first_detected': datetime.utcnow(),
                'last_detected': datetime.utcnow(),
                'detection_count': 1,
                'risk_score': result.get('risk_score', 0),
                'max_risk_score': result.get('risk_score', 0),
                'likelihood': result.get('likelihood', 'Unknown'),
                'max_likelihood': result.get('likelihood', 'Unknown'),
                'latest_risk_score': result.get('risk_score', 0),
                'latest_likelihood': result.get('likelihood', 'Unknown'),
                'is_phishing': result.get('is_phishing', False),
                'analysis_id': analysis_data.get('analysis_id'),
                'latest_analysis_id': analysis_data.get('analysis_id'),
                'user_id': analysis_data.get('user_id'),
                'latest_user_id': analysis_data.get('user_id'),
                'status': 'active',  # active, verified, false_positive
                'verified_by': None,
                'verification_date': None,
                'notes': '',
                'failed_checks': [check for check in result.get('checks', []) if check.get('result') == 'Failed'],
                'warning_checks': [check for check in result.get('checks', []) if check.get('result') == 'Warning'],
                'total_checks': len(result.get('checks', [])),
                'failed_checks_count': len([check for check in result.get('checks', []) if check.get('result') == 'Failed']),
                'warning_checks_count': len([check for check in result.get('checks', []) if check.get('result') == 'Warning'])
            }

            mongo.db.suspicious_links.insert_one(suspicious_link_data)
            print(f"Nouveau lien suspect ajouté: {url} (Score: {result.get('risk_score')}%)")

    except Exception as e:
        print(f"Error saving suspicious link: {str(e)}")

def get_suspicious_links(limit=50, status=None, min_score=30):
    """
    Récupère la liste des liens suspects.

    Args:
        limit (int): Nombre maximum de liens à retourner
        status (str): Filtrer par statut (active, verified, false_positive)
        min_score (int): Score minimum de risque

    Returns:
        list: Liste des liens suspects
    """
    try:
        # Construire le filtre
        filter_query = {'risk_score': {'$gte': min_score}}
        if status:
            filter_query['status'] = status

        # Récupérer les liens suspects
        links = list(mongo.db.suspicious_links.find(filter_query)
                    .sort('last_detected', -1)
                    .limit(limit))

        # Convertir les ObjectId en strings
        for link in links:
            link['_id'] = str(link['_id'])
            if 'first_detected' in link:
                link['first_detected'] = link['first_detected'].isoformat()
            if 'last_detected' in link:
                link['last_detected'] = link['last_detected'].isoformat()
            if 'verification_date' in link and link['verification_date']:
                link['verification_date'] = link['verification_date'].isoformat()

        return links
    except Exception as e:
        print(f"Error retrieving suspicious links: {str(e)}")
        return []

def get_analysis_by_id(analysis_id):
    """
    Récupère une analyse par son ID.
    
    Args:
        analysis_id (str): ID de l'analyse
        
    Returns:
        dict: Données de l'analyse ou None si non trouvée
    """
    try:
        analysis = mongo.db.phishing_analyses.find_one({'_id': ObjectId(analysis_id)})
        if analysis:
            # Convertir récursivement tous les ObjectId
            analysis = convert_objectid_to_string(analysis)
            # Formater les dates si elles existent
            if 'created_at' in analysis and hasattr(analysis['created_at'], 'isoformat'):
                analysis['created_at'] = analysis['created_at'].isoformat()
            if 'completed_at' in analysis and analysis['completed_at'] and hasattr(analysis['completed_at'], 'isoformat'):
                analysis['completed_at'] = analysis['completed_at'].isoformat()
        return analysis
    except Exception as e:
        print(f"Error retrieving analysis: {str(e)}")
        return None

def get_user_analyses(user_id, limit=50):
    """
    Récupère les analyses d'un utilisateur.
    
    Args:
        user_id (str): ID de l'utilisateur
        limit (int): Nombre maximum d'analyses à retourner
        
    Returns:
        list: Liste des analyses de l'utilisateur
    """
    try:
        analyses = list(mongo.db.phishing_analyses.find(
            {'user_id': user_id}
        ).sort('created_at', -1).limit(limit))

        # Convertir tous les ObjectId en strings
        converted_analyses = []
        for analysis in analyses:
            converted_analysis = convert_objectid_to_string(analysis)
            # Formater les dates si elles existent
            if 'created_at' in converted_analysis and hasattr(converted_analysis['created_at'], 'isoformat'):
                converted_analysis['created_at'] = converted_analysis['created_at'].isoformat()
            if 'completed_at' in converted_analysis and converted_analysis['completed_at'] and hasattr(converted_analysis['completed_at'], 'isoformat'):
                converted_analysis['completed_at'] = converted_analysis['completed_at'].isoformat()
            converted_analyses.append(converted_analysis)

        return converted_analyses
    except Exception as e:
        print(f"Error retrieving user analyses: {str(e)}")
        return []

def get_all_analyses(limit=100):
    """
    Récupère toutes les analyses (admin seulement).
    
    Args:
        limit (int): Nombre maximum d'analyses à retourner
        
    Returns:
        list: Liste de toutes les analyses
    """
    try:
        analyses = list(mongo.db.phishing_analyses.find().sort('created_at', -1).limit(limit))

        # Convertir tous les ObjectId en strings
        converted_analyses = []
        for analysis in analyses:
            converted_analysis = convert_objectid_to_string(analysis)
            # Formater les dates si elles existent
            if 'created_at' in converted_analysis and hasattr(converted_analysis['created_at'], 'isoformat'):
                converted_analysis['created_at'] = converted_analysis['created_at'].isoformat()
            if 'completed_at' in converted_analysis and converted_analysis['completed_at'] and hasattr(converted_analysis['completed_at'], 'isoformat'):
                converted_analysis['completed_at'] = converted_analysis['completed_at'].isoformat()
            converted_analyses.append(converted_analysis)

        return converted_analyses
    except Exception as e:
        print(f"Error retrieving all analyses: {str(e)}")
        return []

# ============================================================================
# ROUTES PRINCIPALES
# ============================================================================

@phishing_bp.route('/test', methods=['GET'])
def test_phishing_route():
    """Test route to verify phishing blueprint is working"""
    return jsonify({
        'message': 'Phishing route is working',
        'status': 'ok'
    }), 200

@phishing_bp.route('/test-jwt', methods=['GET'])
@jwt_required()
def test_jwt_route():
    """Test route to verify JWT is working in phishing blueprint"""
    try:
        user_id = get_jwt_identity()
        print(f"DEBUG: JWT test - User ID: {user_id}")
        return jsonify({
            'message': 'JWT is working in phishing route',
            'user_id': user_id,
            'status': 'ok'
        }), 200
    except Exception as e:
        print(f"DEBUG: JWT test error: {str(e)}")
        return jsonify({
            'error': 'JWT test failed',
            'details': str(e)
        }), 500

@phishing_bp.route('/', methods=['GET'])
@jwt_required()
def get_phishing_status():
    """
    Retourne le statut du service de détection de phishing.
    """
    try:
        user_id = get_jwt_identity()
        
        # Statistiques de base
        total_analyses = mongo.db.phishing_analyses.count_documents({})
        user_analyses = mongo.db.phishing_analyses.count_documents({'user_id': user_id})
        
        # Analyses récentes
        recent_analyses = list(mongo.db.phishing_analyses.find(
            {'user_id': user_id}
        ).sort('created_at', -1).limit(5))

        # Convertir tous les ObjectId en strings
        converted_recent_analyses = []
        for analysis in recent_analyses:
            converted_analysis = convert_objectid_to_string(analysis)
            # Formater les dates si elles existent
            if 'created_at' in converted_analysis and hasattr(converted_analysis['created_at'], 'isoformat'):
                converted_analysis['created_at'] = converted_analysis['created_at'].isoformat()
            if 'completed_at' in converted_analysis and converted_analysis['completed_at'] and hasattr(converted_analysis['completed_at'], 'isoformat'):
                converted_analysis['completed_at'] = converted_analysis['completed_at'].isoformat()
            converted_recent_analyses.append(converted_analysis)
        
        return jsonify({
            'status': 'active',
            'service': 'Phishing Detection Service',
            'version': '1.0',
            'statistics': {
                'total_analyses': total_analyses,
                'user_analyses': user_analyses,
                'active_analyses': len(active_analyses)
            },
            'recent_analyses': converted_recent_analyses,
            'capabilities': [
                'URL Analysis',
                'Domain Reputation Check',
                'Content Analysis',
                'Brand Impersonation Detection',
                'Homoglyph Attack Detection',
                'Real-time Scanning'
            ]
        }), 200
        
    except Exception as e:
        return jsonify({
            'error': 'Failed to get phishing service status',
            'details': str(e)
        }), 500

@phishing_bp.route('/analyze', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def analyze_phishing():
    """
    Lance une analyse de phishing sur une URL.
    """
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Validation des données
        if not data or 'url' not in data:
            return jsonify({
                'error': 'URL is required',
                'details': 'Please provide a URL to analyze'
            }), 400
        
        url = data['url'].strip()
        if not url:
            return jsonify({
                'error': 'URL cannot be empty',
                'details': 'Please provide a valid URL'
            }), 400
        
        # Générer un ID unique pour l'analyse
        analysis_id = str(uuid.uuid4())
        
        # Préparer les données de l'analyse
        analysis_data = {
            'analysis_id': analysis_id,
            'user_id': user_id,
            'url': url,
            'status': 'running',
            'started_at': datetime.utcnow(),
            'progress': 0
        }
        
        # Ajouter à la liste des analyses actives
        active_analyses[analysis_id] = analysis_data
        
        # Lancer l'analyse en arrière-plan
        def run_analysis():
            try:
                # Mettre à jour le statut
                active_analyses[analysis_id]['status'] = 'analyzing'
                active_analyses[analysis_id]['progress'] = 10
                
                # Lancer l'analyse avec le service
                result = analyze_url(url)
                
                # Mettre à jour les résultats
                active_analyses[analysis_id].update({
                    'status': 'completed',
                    'progress': 100,
                    'completed_at': datetime.utcnow(),
                    'result': result
                })
                
                # Sauvegarder en base de données
                db_id = save_analysis_to_db(active_analyses[analysis_id])
                if db_id:
                    active_analyses[analysis_id]['db_id'] = db_id
                
                # Nettoyer après 5 minutes
                def cleanup():
                    time.sleep(300)  # 5 minutes
                    if analysis_id in active_analyses:
                        del active_analyses[analysis_id]
                
                threading.Thread(target=cleanup, daemon=True).start()
                
            except Exception as e:
                # Handle errors
                active_analyses[analysis_id].update({
                    'status': 'error',
                    'progress': 0,
                    'error': str(e),
                    'completed_at': datetime.utcnow()
                })
        
        # Démarrer l'analyse en arrière-plan
        threading.Thread(target=run_analysis, daemon=True).start()
        
        return jsonify({
            'message': 'Analysis started successfully',
            'analysis_id': analysis_id,
            'status': 'running',
            'url': url
        }), 202
        
    except Exception as e:
        return jsonify({
            'error': 'Failed to start phishing analysis',
            'details': str(e)
        }), 500

@phishing_bp.route('/status/<analysis_id>', methods=['GET'])
@jwt_required()
def get_analysis_status(analysis_id):
    """
    Recupere le statut d'une analyse en cours.
    """
    try:
        user_id = get_jwt_identity()

        # Vérifier si l'analyse est en cours
        if analysis_id in active_analyses:
            analysis = active_analyses[analysis_id]

            # Vérifier que l'utilisateur a le droit de voir cette analyse
            if analysis['user_id'] != user_id:
                return jsonify({
                    'error': 'Access denied',
                    'details': 'You can only view your own analyses'
                }), 403

            return jsonify({
                'analysis_id': analysis_id,
                'status': analysis['status'],
                'progress': analysis['progress'],
                'url': analysis['url'],
                'started_at': analysis['started_at'].isoformat(),
                'result': analysis.get('result'),
                'error': analysis.get('error')
            }), 200

        # Si pas en cours, chercher en base de données
        analysis = get_analysis_by_id(analysis_id)
        if not analysis:
            return jsonify({
                'error': 'Analysis not found',
                'details': f'No analysis found with ID {analysis_id}'
            }), 404

        # Vérifier les permissions
        if analysis['user_id'] != user_id:
            return jsonify({
                'error': 'Access denied',
                'details': 'You can only view your own analyses'
            }), 403

        return jsonify({
            'analysis_id': analysis_id,
            'status': 'completed',
            'progress': 100,
            'url': analysis['url'],
            'started_at': analysis['started_at'].isoformat(),
            'completed_at': analysis.get('completed_at', '').isoformat() if analysis.get('completed_at') else None,
            'result': analysis.get('result'),
            'error': analysis.get('error')
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get analysis status',
            'details': str(e)
        }), 500

@phishing_bp.route('/history', methods=['GET'])
@jwt_required()
def get_analysis_history():
    """
    Recupere l'historique des analyses de l'utilisateur.
    """
    try:
        user_id = get_jwt_identity()

        # Paramètres de pagination
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        limit = min(limit, 100)  # Limiter à 100 max

        skip = (page - 1) * limit

        # Récupérer les analyses
        analyses = list(mongo.db.phishing_analyses.find(
            {'user_id': user_id}
        ).sort('created_at', -1).skip(skip).limit(limit))

        # Convertir tous les ObjectId en strings pour éviter les erreurs de sérialisation JSON
        converted_analyses = []
        for analysis in analyses:
            # Convertir récursivement tous les ObjectId
            converted_analysis = convert_objectid_to_string(analysis)

            # Formater les dates si elles existent
            if 'created_at' in converted_analysis and hasattr(converted_analysis['created_at'], 'isoformat'):
                converted_analysis['created_at'] = converted_analysis['created_at'].isoformat()
            if 'completed_at' in converted_analysis and converted_analysis['completed_at'] and hasattr(converted_analysis['completed_at'], 'isoformat'):
                converted_analysis['completed_at'] = converted_analysis['completed_at'].isoformat()

            converted_analyses.append(converted_analysis)

        # Compter le total
        total = mongo.db.phishing_analyses.count_documents({'user_id': user_id})

        return jsonify({
            'analyses': converted_analyses,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            }
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get analysis history',
            'details': str(e)
        }), 500

@phishing_bp.route('/quick-check', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def quick_phishing_check():
    """
    Effectue une verification rapide de phishing (synchrone).
    """
    try:
        user_id = get_jwt_identity()
        data = request.get_json()

        # Validation des données
        if not data or 'url' not in data:
            return jsonify({
                'error': 'URL is required',
                'details': 'Please provide a URL to analyze'
            }), 400

        url = data['url'].strip()
        if not url:
            return jsonify({
                'error': 'URL cannot be empty',
                'details': 'Please provide a valid URL'
            }), 400

        # Lancer l'analyse directement (synchrone)
        result = analyze_url(url)

        # Préparer les données pour la sauvegarde
        analysis_data = {
            'analysis_id': str(uuid.uuid4()),
            'user_id': user_id,
            'url': url,
            'status': 'completed',
            'started_at': datetime.utcnow(),
            'completed_at': datetime.utcnow(),
            'result': result,
            'analysis_type': 'quick_check'
        }

        # Sauvegarder en base de données
        db_id = save_analysis_to_db(analysis_data)

        return jsonify({
            'message': 'Quick analysis completed',
            'analysis_id': analysis_data['analysis_id'],
            'db_id': db_id,
            'url': url,
            'result': result
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to perform quick phishing check',
            'details': str(e)
        }), 500

# ============================================================================
# ROUTES D'ADMINISTRATION
# ============================================================================

@phishing_bp.route('/admin/all-analyses', methods=['GET'])
@jwt_required()
@admin_required
def get_all_analyses_admin():
    """
    Recupere toutes les analyses (admin seulement).
    """
    try:
        # Parametres de pagination
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 50, type=int)
        limit = min(limit, 200)  # Limiter a 200 max pour admin

        skip = (page - 1) * limit

        # Recuperer toutes les analyses
        analyses = list(mongo.db.phishing_analyses.find().sort('created_at', -1).skip(skip).limit(limit))

        # Convertir tous les ObjectId en strings et formater les dates
        converted_analyses = []
        for analysis in analyses:
            converted_analysis = convert_objectid_to_string(analysis)
            # Formater les dates si elles existent
            if 'created_at' in converted_analysis and hasattr(converted_analysis['created_at'], 'isoformat'):
                converted_analysis['created_at'] = converted_analysis['created_at'].isoformat()
            if 'completed_at' in converted_analysis and converted_analysis['completed_at'] and hasattr(converted_analysis['completed_at'], 'isoformat'):
                converted_analysis['completed_at'] = converted_analysis['completed_at'].isoformat()
            converted_analyses.append(converted_analysis)

        # Compter le total
        total = mongo.db.phishing_analyses.count_documents({})

        # Statistiques supplementaires
        stats = {
            'total_analyses': total,
            'completed_analyses': mongo.db.phishing_analyses.count_documents({'status': 'completed'}),
            'failed_analyses': mongo.db.phishing_analyses.count_documents({'status': 'error'}),
            'active_analyses': len(active_analyses)
        }

        return jsonify({
            'analyses': converted_analyses,
            'statistics': stats,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            }
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get all analyses',
            'details': str(e)
        }), 500

@phishing_bp.route('/admin/delete/<analysis_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_analysis_admin(analysis_id):
    """
    Supprime une analyse (admin seulement).
    """
    try:
        # Supprimer de la base de donnees
        result = mongo.db.phishing_analyses.delete_one({'_id': ObjectId(analysis_id)})

        if result.deleted_count == 0:
            return jsonify({
                'error': 'Analysis not found',
                'details': f'No analysis found with ID {analysis_id}'
            }), 404

        # Supprimer des analyses actives si presente
        if analysis_id in active_analyses:
            del active_analyses[analysis_id]

        return jsonify({
            'message': 'Analysis deleted successfully',
            'analysis_id': analysis_id
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to delete analysis',
            'details': str(e)
        }), 500

@phishing_bp.route('/admin/stats', methods=['GET'])
@jwt_required()
@admin_required
def get_phishing_stats():
    """
    Recupere les statistiques detaillees du service de phishing.
    """
    try:
        # Statistiques de base
        total_analyses = mongo.db.phishing_analyses.count_documents({})
        completed_analyses = mongo.db.phishing_analyses.count_documents({'status': 'completed'})
        failed_analyses = mongo.db.phishing_analyses.count_documents({'status': 'error'})

        # Analyses par type
        quick_checks = mongo.db.phishing_analyses.count_documents({'analysis_type': 'quick_check'})
        full_analyses = mongo.db.phishing_analyses.count_documents({'analysis_type': 'phishing_detection'})

        # Analyses par resultat de risque
        high_risk = mongo.db.phishing_analyses.count_documents({'result.likelihood': 'High Risk'})
        medium_risk = mongo.db.phishing_analyses.count_documents({'result.likelihood': 'Medium Risk'})
        low_risk = mongo.db.phishing_analyses.count_documents({'result.likelihood': 'Low Risk'})

        # Top utilisateurs
        pipeline = [
            {'$group': {'_id': '$user_id', 'count': {'$sum': 1}}},
            {'$sort': {'count': -1}},
            {'$limit': 10}
        ]
        top_users = list(mongo.db.phishing_analyses.aggregate(pipeline))

        return jsonify({
            'overview': {
                'total_analyses': total_analyses,
                'completed_analyses': completed_analyses,
                'failed_analyses': failed_analyses,
                'active_analyses': len(active_analyses)
            },
            'analysis_types': {
                'quick_checks': quick_checks,
                'full_analyses': full_analyses
            },
            'risk_distribution': {
                'high_risk': high_risk,
                'medium_risk': medium_risk,
                'low_risk': low_risk
            },
            'top_users': top_users
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get phishing statistics',
            'details': str(e)
        }), 500

# ============================================================================
# ROUTES UTILITAIRES
# ============================================================================

@phishing_bp.route('/active-analyses', methods=['GET'])
@jwt_required()
def get_active_analyses():
    """
    Recupere la liste des analyses actives de l'utilisateur.
    """
    try:
        user_id = get_jwt_identity()

        # Filtrer les analyses actives de l'utilisateur
        user_active_analyses = {
            aid: analysis for aid, analysis in active_analyses.items()
            if analysis['user_id'] == user_id
        }

        # Formater les donnees
        formatted_analyses = []
        for analysis_id, analysis in user_active_analyses.items():
            formatted_analyses.append({
                'analysis_id': analysis_id,
                'url': analysis['url'],
                'status': analysis['status'],
                'progress': analysis['progress'],
                'started_at': analysis['started_at'].isoformat()
            })

        return jsonify({
            'active_analyses': formatted_analyses,
            'count': len(formatted_analyses)
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get active analyses',
            'details': str(e)
        }), 500

# ============================================================================
# ROUTES POUR LA GESTION DES LIENS SUSPECTS
# ============================================================================

@phishing_bp.route('/suspicious-links', methods=['GET'])
@jwt_required()
def get_suspicious_links_list():
    """
    Recupere la liste des liens suspects detectes.
    """
    try:
        # Parametres de requete
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 20, type=int)
        status = request.args.get('status', None)
        min_score = request.args.get('min_score', 30, type=int)

        limit = min(limit, 100)  # Limiter a 100 max
        skip = (page - 1) * limit

        # Construire le filtre
        filter_query = {'risk_score': {'$gte': min_score}}
        if status:
            filter_query['status'] = status

        # Recuperer les liens suspects avec pagination
        links = list(mongo.db.suspicious_links.find(filter_query)
                    .sort('last_detected', -1)
                    .skip(skip)
                    .limit(limit))

        # Convertir les ObjectId et formater les dates
        for link in links:
            link['_id'] = str(link['_id'])
            if 'first_detected' in link:
                link['first_detected'] = link['first_detected'].isoformat()
            if 'last_detected' in link:
                link['last_detected'] = link['last_detected'].isoformat()
            if 'verification_date' in link and link['verification_date']:
                link['verification_date'] = link['verification_date'].isoformat()

        # Compter le total
        total = mongo.db.suspicious_links.count_documents(filter_query)

        # Statistiques
        stats = {
            'total_suspicious': total,
            'active': mongo.db.suspicious_links.count_documents({'status': 'active', 'risk_score': {'$gte': min_score}}),
            'verified': mongo.db.suspicious_links.count_documents({'status': 'verified', 'risk_score': {'$gte': min_score}}),
            'false_positive': mongo.db.suspicious_links.count_documents({'status': 'false_positive', 'risk_score': {'$gte': min_score}}),
            'high_risk': mongo.db.suspicious_links.count_documents({'risk_score': {'$gte': 70}}),
            'medium_risk': mongo.db.suspicious_links.count_documents({'risk_score': {'$gte': 40, '$lt': 70}})
        }

        return jsonify({
            'suspicious_links': links,
            'statistics': stats,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total,
                'pages': (total + limit - 1) // limit
            },
            'filters': {
                'status': status,
                'min_score': min_score
            }
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get suspicious links',
            'details': str(e)
        }), 500

@phishing_bp.route('/suspicious-links/<link_id>', methods=['GET'])
@jwt_required()
def get_suspicious_link_details(link_id):
    """
    Recupere les details d'un lien suspect specifique.
    """
    try:
        # Recuperer le lien suspect
        link = mongo.db.suspicious_links.find_one({'_id': ObjectId(link_id)})

        if not link:
            return jsonify({
                'error': 'Suspicious link not found',
                'details': f'No suspicious link found with ID {link_id}'
            }), 404

        # Convertir les ObjectId et formater les dates
        link['_id'] = str(link['_id'])
        if 'first_detected' in link:
            link['first_detected'] = link['first_detected'].isoformat()
        if 'last_detected' in link:
            link['last_detected'] = link['last_detected'].isoformat()
        if 'verification_date' in link and link['verification_date']:
            link['verification_date'] = link['verification_date'].isoformat()

        # Recuperer les analyses associees
        related_analyses = list(mongo.db.phishing_analyses.find(
            {'url': link['url']}
        ).sort('created_at', -1).limit(10))

        # Convertir tous les ObjectId en strings
        converted_related_analyses = []
        for analysis in related_analyses:
            converted_analysis = convert_objectid_to_string(analysis)
            # Formater les dates si elles existent
            if 'created_at' in converted_analysis and hasattr(converted_analysis['created_at'], 'isoformat'):
                converted_analysis['created_at'] = converted_analysis['created_at'].isoformat()
            if 'completed_at' in converted_analysis and converted_analysis['completed_at'] and hasattr(converted_analysis['completed_at'], 'isoformat'):
                converted_analysis['completed_at'] = converted_analysis['completed_at'].isoformat()
            converted_related_analyses.append(converted_analysis)

        return jsonify({
            'suspicious_link': link,
            'related_analyses': converted_related_analyses
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get suspicious link details',
            'details': str(e)
        }), 500

@phishing_bp.route('/suspicious-links/<link_id>/verify', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
@admin_required
def verify_suspicious_link(link_id):
    """
    Verifie un lien suspect (admin seulement).
    """
    try:
        user_id = get_jwt_identity()
        data = request.get_json() or {}

        # Parametres de verification
        status = data.get('status', 'verified')  # verified, false_positive
        notes = data.get('notes', '')

        if status not in ['verified', 'false_positive']:
            return jsonify({
                'error': 'Invalid status',
                'details': 'Status must be either "verified" or "false_positive"'
            }), 400

        # Mettre a jour le lien suspect
        update_data = {
            'status': status,
            'verified_by': user_id,
            'verification_date': datetime.utcnow(),
            'notes': notes
        }

        result = mongo.db.suspicious_links.update_one(
            {'_id': ObjectId(link_id)},
            {'$set': update_data}
        )

        if result.matched_count == 0:
            return jsonify({
                'error': 'Suspicious link not found',
                'details': f'No suspicious link found with ID {link_id}'
            }), 404

        return jsonify({
            'message': 'Suspicious link verified successfully',
            'link_id': link_id,
            'status': status,
            'verified_by': user_id
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to verify suspicious link',
            'details': str(e)
        }), 500

@phishing_bp.route('/suspicious-links/<link_id>', methods=['DELETE'])
@jwt_required()
@admin_required
def delete_suspicious_link(link_id):
    """
    Supprime un lien suspect (admin seulement).
    """
    try:
        # Supprimer le lien suspect
        result = mongo.db.suspicious_links.delete_one({'_id': ObjectId(link_id)})

        if result.deleted_count == 0:
            return jsonify({
                'error': 'Suspicious link not found',
                'details': f'No suspicious link found with ID {link_id}'
            }), 404

        return jsonify({
            'message': 'Suspicious link deleted successfully',
            'link_id': link_id
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to delete suspicious link',
            'details': str(e)
        }), 500

@phishing_bp.route('/suspicious-links/stats', methods=['GET'])
@jwt_required()
@admin_required
def get_suspicious_links_stats():
    """
    Recupere les statistiques detaillees des liens suspects (admin seulement).
    """
    try:
        # Statistiques generales
        total_suspicious = mongo.db.suspicious_links.count_documents({})
        active_links = mongo.db.suspicious_links.count_documents({'status': 'active'})
        verified_phishing = mongo.db.suspicious_links.count_documents({'status': 'verified'})
        false_positives = mongo.db.suspicious_links.count_documents({'status': 'false_positive'})

        # Repartition par score de risque (New thresholds: 0-30% Low, 31-50% Medium, 51-100% High)
        high_risk = mongo.db.suspicious_links.count_documents({'risk_score': {'$gt': 50}})
        medium_risk = mongo.db.suspicious_links.count_documents({'risk_score': {'$gt': 30, '$lte': 50}})
        low_risk = mongo.db.suspicious_links.count_documents({'risk_score': {'$lte': 30}})

        # Top domaines suspects
        pipeline_domains = [
            {'$group': {'_id': '$domain', 'count': {'$sum': 1}, 'avg_score': {'$avg': '$risk_score'}}},
            {'$sort': {'count': -1}},
            {'$limit': 10}
        ]
        top_domains = list(mongo.db.suspicious_links.aggregate(pipeline_domains))

        # Liens les plus detectes
        pipeline_frequent = [
            {'$sort': {'detection_count': -1}},
            {'$limit': 10},
            {'$project': {'url': 1, 'detection_count': 1, 'risk_score': 1, 'status': 1}}
        ]
        most_detected = list(mongo.db.suspicious_links.aggregate(pipeline_frequent))

        # Convertir ObjectId en string pour most_detected
        for link in most_detected:
            link['_id'] = str(link['_id'])

        # Evolution temporelle (derniers 30 jours)
        from datetime import timedelta
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)

        pipeline_timeline = [
            {'$match': {'first_detected': {'$gte': thirty_days_ago}}},
            {'$group': {
                '_id': {
                    'year': {'$year': '$first_detected'},
                    'month': {'$month': '$first_detected'},
                    'day': {'$dayOfMonth': '$first_detected'}
                },
                'count': {'$sum': 1}
            }},
            {'$sort': {'_id': 1}}
        ]
        timeline_data = list(mongo.db.suspicious_links.aggregate(pipeline_timeline))

        # Statistiques par utilisateur (top detecteurs)
        pipeline_users = [
            {'$group': {'_id': '$user_id', 'detections': {'$sum': 1}}},
            {'$sort': {'detections': -1}},
            {'$limit': 10}
        ]
        top_detectors = list(mongo.db.suspicious_links.aggregate(pipeline_users))

        return jsonify({
            'overview': {
                'total_suspicious': total_suspicious,
                'active_links': active_links,
                'verified_phishing': verified_phishing,
                'false_positives': false_positives,
                'verification_rate': round((verified_phishing + false_positives) / max(total_suspicious, 1) * 100, 2)
            },
            'risk_distribution': {
                'high_risk': high_risk,
                'medium_risk': medium_risk,
                'low_risk': low_risk
            },
            'top_domains': top_domains,
            'most_detected_links': most_detected,
            'timeline_30_days': timeline_data,
            'top_detectors': top_detectors
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to get suspicious links statistics',
            'details': str(e)
        }), 500

@phishing_bp.route('/suspicious-links/export', methods=['GET'])
@jwt_required()
@admin_required
def export_suspicious_links():
    """
    Exporte la liste des liens suspects au format JSON (admin seulement).
    """
    try:
        # Parametres d'export
        status = request.args.get('status', None)
        min_score = request.args.get('min_score', 30, type=int)
        format_type = request.args.get('format', 'json')  # json, csv

        # Construire le filtre
        filter_query = {'risk_score': {'$gte': min_score}}
        if status:
            filter_query['status'] = status

        # Recuperer tous les liens correspondants
        links = list(mongo.db.suspicious_links.find(filter_query).sort('risk_score', -1))

        # Preparer les donnees d'export
        export_data = []
        for link in links:
            export_item = {
                'id': str(link['_id']),
                'url': link['url'],
                'domain': link['domain'],
                'risk_score': link['risk_score'],
                'likelihood': link['likelihood'],
                'status': link['status'],
                'detection_count': link['detection_count'],
                'first_detected': link['first_detected'].isoformat(),
                'last_detected': link['last_detected'].isoformat(),
                'is_phishing': link.get('is_phishing', False),
                'failed_checks_count': link.get('failed_checks_count', 0),
                'warning_checks_count': link.get('warning_checks_count', 0),
                'verified_by': link.get('verified_by'),
                'verification_date': link.get('verification_date').isoformat() if link.get('verification_date') else None,
                'notes': link.get('notes', '')
            }
            export_data.append(export_item)

        if format_type == 'csv':
            # TODO: Implementer l'export CSV si necessaire
            return jsonify({
                'error': 'CSV export not implemented yet',
                'details': 'Use JSON format for now'
            }), 501

        return jsonify({
            'export_data': export_data,
            'metadata': {
                'export_date': datetime.utcnow().isoformat(),
                'total_links': len(export_data),
                'filters': {
                    'status': status,
                    'min_score': min_score
                }
            }
        }), 200

    except Exception as e:
        return jsonify({
            'error': 'Failed to export suspicious links',
            'details': str(e)
        }), 500
